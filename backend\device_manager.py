"""
ADB设备管理模块
集成现有的douyin_adb.py功能，提供设备扫描、连接、状态监控等功能
"""

import asyncio
import subprocess
import re
import logging
from typing import List, Dict, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class DeviceManager:
    """设备管理器"""
    
    def __init__(self):
        self.devices = {}
        self.device_status_cache = {}
        
    async def initialize(self):
        """初始化设备管理器"""
        logger.info("初始化设备管理器...")
        await self.scan_devices()
        
    async def scan_devices(self) -> List[Dict]:
        """扫描ADB设备"""
        try:
            logger.info("扫描ADB设备...")
            
            # 运行adb devices命令
            process = await asyncio.create_subprocess_exec(
                'adb', 'devices',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"ADB命令执行失败: {stderr.decode()}")
                return []
            
            # 解析输出结果
            output = stdout.decode()
            devices = []
            
            for line in output.split('\n'):
                match = re.match(r'([\w\d:.]+)\s+(device|offline)', line.strip())
                if match:
                    device_id = match.group(1)
                    device_status = match.group(2)
                    
                    # 获取设备详细信息
                    device_info = await self.get_device_info(device_id)
                    device_info.update({
                        "id": device_id,
                        "status": "online" if device_status == "device" else "offline",
                        "last_seen": datetime.now().isoformat()
                    })
                    
                    devices.append(device_info)
                    self.devices[device_id] = device_info
            
            logger.info(f"发现 {len(devices)} 个设备")
            return devices
            
        except FileNotFoundError:
            logger.error("未找到adb命令，请确保已安装Android SDK并将adb添加到系统PATH中")
            return []
        except Exception as e:
            logger.error(f"扫描设备失败: {e}")
            return []
    
    async def get_device_info(self, device_id: str) -> Dict:
        """获取设备详细信息"""
        device_info = {
            "name": device_id,
            "model": "Unknown",
            "android_version": "Unknown",
            "screen_size": "Unknown",
            "battery_level": "Unknown"
        }
        
        try:
            # 获取设备型号
            model = await self.run_adb_command(device_id, ['shell', 'getprop', 'ro.product.model'])
            if model:
                device_info["model"] = model.strip()
                device_info["name"] = model.strip()
            
            # 获取Android版本
            version = await self.run_adb_command(device_id, ['shell', 'getprop', 'ro.build.version.release'])
            if version:
                device_info["android_version"] = version.strip()
            
            # 获取屏幕尺寸
            screen_size = await self.get_screen_size(device_id)
            if screen_size:
                device_info["screen_size"] = f"{screen_size['width']}x{screen_size['height']}"
            
            # 获取电池电量
            battery = await self.run_adb_command(device_id, ['shell', 'dumpsys', 'battery'])
            if battery:
                battery_match = re.search(r'level: (\d+)', battery)
                if battery_match:
                    device_info["battery_level"] = f"{battery_match.group(1)}%"
        
        except Exception as e:
            logger.warning(f"获取设备 {device_id} 信息失败: {e}")
        
        return device_info
    
    async def get_screen_size(self, device_id: str) -> Optional[Dict]:
        """获取设备屏幕尺寸"""
        try:
            output = await self.run_adb_command(device_id, ['shell', 'wm', 'size'])
            if output and 'Physical size:' in output:
                for line in output.split('\n'):
                    if 'Physical size:' in line:
                        size_str = line.split('Physical size: ')[1].strip()
                        if 'x' in size_str:
                            parts = size_str.split('x')
                            if len(parts) >= 2:
                                return {
                                    'width': int(parts[0]),
                                    'height': int(parts[1])
                                }
        except Exception as e:
            logger.warning(f"获取设备 {device_id} 屏幕尺寸失败: {e}")
        
        return None
    
    async def run_adb_command(self, device_id: str, command: List[str]) -> Optional[str]:
        """运行ADB命令"""
        try:
            full_command = ['adb', '-s', device_id] + command
            process = await asyncio.create_subprocess_exec(
                *full_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return stdout.decode()
            else:
                logger.warning(f"ADB命令执行失败: {stderr.decode()}")
                return None
                
        except Exception as e:
            logger.error(f"执行ADB命令失败: {e}")
            return None
    
    async def connect_device(self, device_id: str) -> bool:
        """连接设备"""
        try:
            logger.info(f"连接设备: {device_id}")
            
            # 检查设备是否存在
            if device_id not in self.devices:
                logger.error(f"设备 {device_id} 不存在")
                return False
            
            # 尝试连接设备
            result = await self.run_adb_command(device_id, ['shell', 'echo', 'test'])
            if result is not None:
                self.devices[device_id]["status"] = "online"
                self.devices[device_id]["last_seen"] = datetime.now().isoformat()
                logger.info(f"设备 {device_id} 连接成功")
                return True
            else:
                self.devices[device_id]["status"] = "offline"
                logger.error(f"设备 {device_id} 连接失败")
                return False
                
        except Exception as e:
            logger.error(f"连接设备 {device_id} 失败: {e}")
            return False
    
    async def disconnect_device(self, device_id: str) -> bool:
        """断开设备连接"""
        try:
            if device_id in self.devices:
                self.devices[device_id]["status"] = "offline"
                logger.info(f"设备 {device_id} 已断开连接")
                return True
            return False
        except Exception as e:
            logger.error(f"断开设备 {device_id} 连接失败: {e}")
            return False
    
    async def get_devices(self) -> List[Dict]:
        """获取设备列表"""
        return list(self.devices.values())
    
    async def get_device(self, device_id: str) -> Optional[Dict]:
        """获取单个设备信息"""
        return self.devices.get(device_id)
    
    async def click_device(self, device_id: str, x: int, y: int) -> bool:
        """在设备上执行点击操作"""
        try:
            result = await self.run_adb_command(
                device_id, 
                ['shell', 'input', 'tap', str(x), str(y)]
            )
            return result is not None
        except Exception as e:
            logger.error(f"设备 {device_id} 点击操作失败: {e}")
            return False
    
    async def swipe_device(self, device_id: str, x1: int, y1: int, x2: int, y2: int, duration: int = 200) -> bool:
        """在设备上执行滑动操作"""
        try:
            result = await self.run_adb_command(
                device_id,
                ['shell', 'input', 'swipe', str(x1), str(y1), str(x2), str(y2), str(duration)]
            )
            return result is not None
        except Exception as e:
            logger.error(f"设备 {device_id} 滑动操作失败: {e}")
            return False
    
    async def input_text(self, device_id: str, text: str) -> bool:
        """在设备上输入文本"""
        try:
            # 转义特殊字符
            escaped_text = text.replace(' ', '%s').replace('&', '\\&')
            result = await self.run_adb_command(
                device_id,
                ['shell', 'input', 'text', escaped_text]
            )
            return result is not None
        except Exception as e:
            logger.error(f"设备 {device_id} 文本输入失败: {e}")
            return False
    
    async def press_key(self, device_id: str, keycode: int) -> bool:
        """在设备上按键"""
        try:
            result = await self.run_adb_command(
                device_id,
                ['shell', 'input', 'keyevent', str(keycode)]
            )
            return result is not None
        except Exception as e:
            logger.error(f"设备 {device_id} 按键操作失败: {e}")
            return False
    
    async def install_app(self, device_id: str, apk_path: str) -> bool:
        """在设备上安装应用"""
        try:
            result = await self.run_adb_command(device_id, ['install', apk_path])
            return result is not None and 'Success' in result
        except Exception as e:
            logger.error(f"设备 {device_id} 应用安装失败: {e}")
            return False
    
    async def start_app(self, device_id: str, package_name: str) -> bool:
        """启动应用"""
        try:
            result = await self.run_adb_command(
                device_id,
                ['shell', 'monkey', '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1']
            )
            return result is not None
        except Exception as e:
            logger.error(f"设备 {device_id} 启动应用失败: {e}")
            return False
