"""
Android设备群控系统后端服务器
使用FastAPI + WebSocket实现实时通信
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import asyncio
import json
import logging
from datetime import datetime
from typing import List, Dict, Any
import uvicorn

from device_manager import DeviceManager
from script_manager import ScriptManager
from scrcpy_manager import ScrcpyManager
from websocket_manager import WebSocketManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="Android设备群控系统",
    description="基于ADB和Scrcpy的Android设备并发群控系统",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化管理器
device_manager = DeviceManager()
script_manager = ScriptManager()
scrcpy_manager = ScrcpyManager()
websocket_manager = WebSocketManager()

# 全局状态
app_state = {
    "devices": {},
    "scripts": {},
    "running_scripts": {},
    "logs": []
}

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("启动Android设备群控系统...")
    
    # 初始化设备管理器
    await device_manager.initialize()
    
    # 启动设备监控任务
    asyncio.create_task(device_monitor_task())
    
    logger.info("系统启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("关闭系统...")
    
    # 停止所有scrcpy进程
    await scrcpy_manager.stop_all()
    
    # 停止所有运行中的脚本
    await script_manager.stop_all_scripts()
    
    logger.info("系统已关闭")

# WebSocket连接管理
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await websocket_manager.connect(websocket)
    try:
        # 发送初始数据
        await send_initial_data(websocket)
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理不同类型的消息
            await handle_websocket_message(websocket, message)
            
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
        logger.info("WebSocket客户端断开连接")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        websocket_manager.disconnect(websocket)

async def send_initial_data(websocket: WebSocket):
    """发送初始数据给新连接的客户端"""
    # 发送设备列表
    devices = await device_manager.get_devices()
    await websocket.send_text(json.dumps({
        "type": "devices_update",
        "data": devices
    }))
    
    # 发送脚本列表
    scripts = await script_manager.get_scripts()
    await websocket.send_text(json.dumps({
        "type": "scripts_update", 
        "data": scripts
    }))

async def handle_websocket_message(websocket: WebSocket, message: Dict[str, Any]):
    """处理WebSocket消息"""
    msg_type = message.get("type")
    data = message.get("data", {})
    
    if msg_type == "scan_devices":
        # 扫描设备
        devices = await device_manager.scan_devices()
        await broadcast_message({
            "type": "devices_update",
            "data": devices
        })
    
    elif msg_type == "connect_device":
        # 连接设备
        device_id = data.get("device_id")
        success = await device_manager.connect_device(device_id)
        if success:
            await broadcast_log({
                "level": "success",
                "message": f"设备 {device_id} 连接成功",
                "device_id": device_id
            })
        else:
            await broadcast_log({
                "level": "error", 
                "message": f"设备 {device_id} 连接失败",
                "device_id": device_id
            })
    
    elif msg_type == "start_scrcpy":
        # 启动scrcpy
        device_id = data.get("device_id")
        success = await scrcpy_manager.start_scrcpy(device_id)
        if success:
            await broadcast_log({
                "level": "success",
                "message": f"设备 {device_id} 屏幕镜像启动成功",
                "device_id": device_id
            })
    
    elif msg_type == "run_script":
        # 运行脚本
        script_id = data.get("script_id")
        device_ids = data.get("device_ids", [])
        await run_script_on_devices(script_id, device_ids)

async def broadcast_message(message: Dict[str, Any]):
    """广播消息给所有连接的客户端"""
    await websocket_manager.broadcast(json.dumps(message))

async def broadcast_log(log_data: Dict[str, Any]):
    """广播日志消息"""
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "level": log_data.get("level", "info"),
        "message": log_data.get("message", ""),
        "script_name": log_data.get("script_name", ""),
        "device_id": log_data.get("device_id", "")
    }
    
    app_state["logs"].append(log_entry)
    
    await broadcast_message({
        "type": "log_update",
        "data": log_entry
    })

async def device_monitor_task():
    """设备监控任务"""
    while True:
        try:
            # 每5秒检查一次设备状态
            devices = await device_manager.get_devices()
            await broadcast_message({
                "type": "devices_update",
                "data": devices
            })
            await asyncio.sleep(5)
        except Exception as e:
            logger.error(f"设备监控任务错误: {e}")
            await asyncio.sleep(10)

async def run_script_on_devices(script_id: str, device_ids: List[str]):
    """在指定设备上运行脚本"""
    script = await script_manager.get_script(script_id)
    if not script:
        await broadcast_log({
            "level": "error",
            "message": f"脚本 {script_id} 不存在"
        })
        return
    
    # 并发在多个设备上执行脚本
    tasks = []
    for device_id in device_ids:
        task = asyncio.create_task(
            script_manager.run_script(script_id, device_id, broadcast_log)
        )
        tasks.append(task)
    
    # 等待所有任务完成
    await asyncio.gather(*tasks, return_exceptions=True)

# REST API端点
@app.get("/")
async def root():
    """根路径"""
    return {"message": "Android设备群控系统API"}

@app.get("/api/devices")
async def get_devices():
    """获取设备列表"""
    devices = await device_manager.get_devices()
    return {"devices": devices}

@app.post("/api/devices/scan")
async def scan_devices():
    """扫描设备"""
    devices = await device_manager.scan_devices()
    return {"devices": devices}

@app.post("/api/devices/{device_id}/connect")
async def connect_device(device_id: str):
    """连接设备"""
    success = await device_manager.connect_device(device_id)
    if success:
        return {"message": f"设备 {device_id} 连接成功"}
    else:
        raise HTTPException(status_code=400, detail=f"设备 {device_id} 连接失败")

@app.get("/api/scripts")
async def get_scripts():
    """获取脚本列表"""
    scripts = await script_manager.get_scripts()
    return {"scripts": scripts}

@app.post("/api/scripts")
async def create_script(script_data: dict):
    """创建新脚本"""
    script = await script_manager.create_script(script_data)
    return {"script": script}

@app.get("/api/logs")
async def get_logs():
    """获取日志"""
    return {"logs": app_state["logs"]}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
